// Temporarily disabled <PERSON><PERSON> to fix performance issues
// import * as Sen<PERSON> from '@sentry/nextjs';
// import { SentryConfig } from './sentry.config';

export async function register() {
  // Sen<PERSON> temporarily disabled for development
  console.log('Instrumentation register called - Sen<PERSON> disabled for performance');
}

export const onRequestError = (error: any) => {
  console.error('Request error:', error);
};
