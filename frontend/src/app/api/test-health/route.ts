import { checkApiHealth } from '@/lib/api';

export async function GET() {
  try {
    console.log('Testing API health check...');
    const result = await checkApiHealth();
    console.log('Health check result:', result);
    return Response.json({ success: true, result });
  } catch (error) {
    console.error('Health check failed:', error);
    return Response.json({ success: false, error: error.message }, { status: 500 });
  }
}
