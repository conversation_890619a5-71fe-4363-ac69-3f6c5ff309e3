'use client';

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';

type User = {
  id: string;
  email: string;
  email_confirmed_at?: string;
};

type AuthContextType = {
  user: User | null;
  isLoading: boolean;
  signOut: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // For demo purposes, skip auth check on initial load to improve performance
    // Just set loading to false immediately
    setIsLoading(false);
    setUser(null);
  }, []);

  const signOut = async () => {
    try {
      // Call the signout action which will clear cookies and redirect
      const { signOut: signOutAction } = await import('@/app/auth/actions');
      await signOutAction();
    } catch (error) {
      console.error('Sign out error:', error);
      // Clear user state even if the API call fails
      setUser(null);
      window.location.href = '/';
    }
  };

  const value = {
    user,
    isLoading,
    signOut,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
