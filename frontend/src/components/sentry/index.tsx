'use client';

import { useEffect } from 'react';
// Temporarily disabled <PERSON><PERSON> to fix performance issues
// import * as Sen<PERSON> from '@sentry/nextjs';
import { useAuth } from '../AuthProvider';

export const VSentry: React.FC = () => {
  const { user } = useAuth();
  useEffect(() => {
    if (!document) return;
    // Temporarily disabled Sentry for performance
    console.log('Sentry user tracking disabled for performance:', user?.email);
  }, [user]);

  return null;
};
