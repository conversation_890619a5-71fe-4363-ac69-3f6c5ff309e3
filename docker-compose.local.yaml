services:
  # Local Supabase services
  db:
    image: supabase/postgres:15.1.1.78
    ports:
      - "54322:5432"
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_PORT: 5432
    volumes:
      - db_data:/var/lib/postgresql/data
    command: >
      postgres
      -c config_file=/etc/postgresql/postgresql.conf
      -c log_min_messages=fatal

  auth:
    image: supabase/gotrue:v2.143.0
    depends_on:
      db:
        condition: service_healthy
    restart: unless-stopped
    environment:
      GOTRUE_API_HOST: 0.0.0.0
      GOTRUE_API_PORT: 9999
      API_EXTERNAL_URL: http://localhost:54321
      GOTRUE_DB_DRIVER: postgres
      GOTRUE_DB_DATABASE_URL: ***********************************************/postgres
      GOTRUE_SITE_URL: http://localhost:3000
      GOTRUE_URI_ALLOW_LIST: "*"
      GOTRUE_DISABLE_SIGNUP: false
      GOTRUE_JWT_ADMIN_ROLES: service_role
      GOTRUE_JWT_AUD: authenticated
      GOTRUE_JWT_DEFAULT_GROUP_NAME: authenticated
      GOTRUE_JWT_EXP: 3600
      GOTRUE_JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
      GOTRUE_EXTERNAL_EMAIL_ENABLED: true
      GOTRUE_MAILER_AUTOCONFIRM: true
      GOTRUE_SMTP_ADMIN_EMAIL: <EMAIL>
      GOTRUE_SMTP_HOST: inbucket
      GOTRUE_SMTP_PORT: 2500
      GOTRUE_SMTP_USER: fake_mail_user
      GOTRUE_SMTP_PASS: fake_mail_password
      GOTRUE_SMTP_SENDER_NAME: fake_sender
    ports:
      - "9999:9999"

  rest:
    image: postgrest/postgrest:v12.0.1
    depends_on:
      db:
        condition: service_healthy
    restart: unless-stopped
    environment:
      PGRST_DB_URI: *****************************************/postgres
      PGRST_DB_SCHEMAS: public,storage,graphql_public
      PGRST_DB_ANON_ROLE: anon
      PGRST_JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
      PGRST_DB_USE_LEGACY_GUCS: "false"
      PGRST_APP_SETTINGS_JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
      PGRST_APP_SETTINGS_JWT_EXP: 3600
    ports:
      - "54321:3000"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./backend/services/docker/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf --save 60 1 --loglevel warning
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  rabbitmq:
    image: rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "-q", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./backend/.env:/app/.env:ro
    env_file:
      - ./backend/.env
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - REDIS_SSL=False
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
    depends_on:
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      worker:
        condition: service_started
      rest:
        condition: service_started

  worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    command: python -m dramatiq run_agent_background
    volumes:
      - ./backend/.env:/app/.env:ro
    env_file:
      - ./backend/.env
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - REDIS_SSL=False
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
    depends_on:
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy

  frontend:
    init: true
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - ./frontend/.env.local:/app/.env.local:ro
    environment:
      - NODE_ENV=production
    command: ["npm", "run", "start"]
    depends_on:
      - backend

volumes:
  db_data:
  redis_data:
  rabbitmq_data:
