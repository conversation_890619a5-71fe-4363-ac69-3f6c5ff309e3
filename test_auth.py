#!/usr/bin/env python3
"""
Test script for the local authentication system
"""
import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_signup():
    """Test user registration"""
    print("Testing user registration...")
    
    data = {
        "email": "<EMAIL>",
        "password": "testpass123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/signup", json=data)
    print(f"Signup response: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Registration successful!")
        print(f"User ID: {result['user']['id']}")
        print(f"Email: {result['user']['email']}")
        return result['access_token']
    else:
        print(f"❌ Registration failed: {response.text}")
        return None

def test_signin():
    """Test user login"""
    print("\nTesting user login...")
    
    data = {
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/signin", json=data)
    print(f"Signin response: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Login successful!")
        print(f"User ID: {result['user']['id']}")
        print(f"Email: {result['user']['email']}")
        return result['access_token']
    else:
        print(f"❌ Login failed: {response.text}")
        return None

def test_get_user(token):
    """Test getting user info with token"""
    print("\nTesting get user info...")
    
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    response = requests.get(f"{BASE_URL}/auth/user", headers=headers)
    print(f"Get user response: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Get user successful!")
        print(f"User ID: {result['id']}")
        print(f"Email: {result['email']}")
        print(f"Created: {result['created_at']}")
        return True
    else:
        print(f"❌ Get user failed: {response.text}")
        return False

def main():
    print("🚀 Testing Local Authentication System")
    print("=" * 50)
    
    # Test signup (might fail if user already exists)
    token = test_signup()
    
    # Test signin
    if not token:
        token = test_signin()
    
    # Test get user info
    if token:
        test_get_user(token)
    
    print("\n" + "=" * 50)
    print("✅ Authentication system test completed!")

if __name__ == "__main__":
    main()
