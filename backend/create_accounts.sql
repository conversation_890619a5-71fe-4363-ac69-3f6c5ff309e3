-- Create auth.users table for compatibility
CREATE TABLE IF NOT EXISTS auth.users (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    email text UNIQUE,
    encrypted_password text,
    email_confirmed_at timestamp with time zone,
    invited_at timestamp with time zone,
    confirmation_token text,
    confirmation_sent_at timestamp with time zone,
    recovery_token text,
    recovery_sent_at timestamp with time zone,
    email_change_token_new text,
    email_change text,
    email_change_sent_at timestamp with time zone,
    last_sign_in_at timestamp with time zone,
    raw_app_meta_data jsonb,
    raw_user_meta_data jsonb,
    is_super_admin boolean,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    phone text,
    phone_confirmed_at timestamp with time zone,
    phone_change text,
    phone_change_token text,
    phone_change_sent_at timestamp with time zone,
    confirmed_at timestamp with time zone,
    email_change_token_current text DEFAULT '',
    email_change_confirm_status smallint DEFAULT 0,
    banned_until timestamp with time zone,
    reauthentication_token text DEFAULT '',
    reauthentication_sent_at timestamp with time zone,
    is_sso_user boolean DEFAULT false NOT NULL,
    deleted_at timestamp with time zone
);

-- Create account role enum
DO $$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM pg_type t JOIN pg_namespace n ON n.oid = t.typnamespace 
                  WHERE t.typname = 'account_role' AND n.nspname = 'basejump') THEN
        CREATE TYPE basejump.account_role AS ENUM ('owner', 'member');
    END IF;
END $$;

-- Create accounts table
CREATE TABLE IF NOT EXISTS basejump.accounts (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    primary_owner_user_id uuid REFERENCES auth.users NOT NULL DEFAULT auth.uid(),
    name text,
    slug text UNIQUE,
    personal_account boolean DEFAULT false NOT NULL,
    updated_at timestamp with time zone DEFAULT now(),
    created_at timestamp with time zone DEFAULT now(),
    created_by uuid REFERENCES auth.users,
    updated_by uuid REFERENCES auth.users,
    private_metadata jsonb DEFAULT '{}'::jsonb,
    public_metadata jsonb DEFAULT '{}'::jsonb
);

-- Add constraint for slug
ALTER TABLE basejump.accounts
ADD CONSTRAINT basejump_accounts_slug_null_if_personal_account_true CHECK (
    (personal_account = true AND slug is null)
    OR (personal_account = false AND slug is not null)
);

-- Create account_user table
CREATE TABLE IF NOT EXISTS basejump.account_user (
    user_id uuid REFERENCES auth.users ON DELETE CASCADE NOT NULL,
    account_id uuid REFERENCES basejump.accounts ON DELETE CASCADE NOT NULL,
    account_role basejump.account_role NOT NULL,
    CONSTRAINT account_user_pkey PRIMARY KEY (user_id, account_id)
);

-- Enable RLS
ALTER TABLE basejump.accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE basejump.account_user ENABLE ROW LEVEL SECURITY;

-- Create utility functions
CREATE OR REPLACE FUNCTION basejump.has_role_on_account(account_id uuid, account_role basejump.account_role DEFAULT NULL)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
SELECT EXISTS(
    SELECT 1
    FROM basejump.account_user wu
    WHERE wu.user_id = auth.uid()
      AND wu.account_id = has_role_on_account.account_id
      AND (wu.account_role = has_role_on_account.account_role OR has_role_on_account.account_role IS NULL)
);
$$;

-- Create function to add user to new account
CREATE OR REPLACE FUNCTION basejump.add_current_user_to_new_account()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    IF NEW.primary_owner_user_id = auth.uid() THEN
        INSERT INTO basejump.account_user (account_id, user_id, account_role)
        VALUES (NEW.id, auth.uid(), 'owner');
    END IF;
    RETURN NEW;
END;
$$;

-- Create trigger for new accounts
CREATE TRIGGER basejump_add_current_user_to_new_account
    AFTER INSERT ON basejump.accounts
    FOR EACH ROW
    EXECUTE FUNCTION basejump.add_current_user_to_new_account();

-- Create function for new user setup
CREATE OR REPLACE FUNCTION basejump.run_new_user_setup()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    first_account_id uuid;
    generated_user_name text;
BEGIN
    IF NEW.email IS NOT NULL THEN
        generated_user_name := split_part(NEW.email, '@', 1);
    END IF;
    
    -- Create personal account
    INSERT INTO basejump.accounts (name, primary_owner_user_id, personal_account, id)
    VALUES (generated_user_name, NEW.id, true, NEW.id)
    RETURNING id INTO first_account_id;

    -- Add to account_user table
    INSERT INTO basejump.account_user (account_id, user_id, account_role)
    VALUES (first_account_id, NEW.id, 'owner');

    RETURN NEW;
END;
$$;

-- Create trigger for new users
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION basejump.run_new_user_setup();

-- Create basic RLS policies
CREATE POLICY "users can view their own account_users" ON basejump.account_user
    FOR SELECT TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "Accounts are viewable by members" ON basejump.accounts
    FOR SELECT TO authenticated
    USING (basejump.has_role_on_account(id) = true);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE basejump.accounts TO authenticated, service_role;
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE basejump.account_user TO authenticated, service_role;
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE auth.users TO authenticated, service_role;
