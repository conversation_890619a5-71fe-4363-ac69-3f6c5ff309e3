"""
Simple authentication API to replace Supabase auth for local development
"""
import hashlib
import jwt
import uuid
from datetime import datetime, timedelta
from typing import Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
# from services.supabase import DBConnection  # Disabled for demo
from utils.logger import logger

router = APIRouter(prefix="/auth", tags=["auth"])

# JWT Secret - in production this should be from environment
JWT_SECRET = "your-secret-key-for-local-development"
JWT_ALGORITHM = "HS256"

class SignUpRequest(BaseModel):
    email: str
    password: str

class SignInRequest(BaseModel):
    email: str
    password: str

class AuthResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    user: dict

def hash_password(password: str) -> str:
    """Hash password using SHA256"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password: str, hashed: str) -> bool:
    """Verify password against hash"""
    return hash_password(password) == hashed

def create_access_token(user_id: str, email: str) -> str:
    """Create JWT access token"""
    expire = datetime.utcnow() + timedelta(hours=24)
    payload = {
        "sub": user_id,
        "email": email,
        "exp": expire,
        "iat": datetime.utcnow(),
        "iss": "suna-local"
    }
    return jwt.encode(payload, JWT_SECRET, algorithm=JWT_ALGORITHM)

@router.post("/signup", response_model=AuthResponse)
async def sign_up(request: SignUpRequest):
    """Register a new user - simplified for demo"""
    try:
        # Simple email validation
        if "@" not in request.email or "." not in request.email:
            raise HTTPException(status_code=400, detail="Invalid email format")

        if len(request.password) < 6:
            raise HTTPException(status_code=400, detail="Password must be at least 6 characters")

        # Create new user (demo - no database)
        user_id = str(uuid.uuid4())

        # Create access token
        access_token = create_access_token(user_id, request.email)

        return AuthResponse(
            access_token=access_token,
            user={
                "id": user_id,
                "email": request.email,
                "email_confirmed_at": datetime.utcnow().isoformat()
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Sign up error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/signin", response_model=AuthResponse)
async def sign_in(request: SignInRequest):
    """Sign in existing user - simplified for demo"""
    try:
        # Simple email validation
        if "@" not in request.email or "." not in request.email:
            raise HTTPException(status_code=401, detail="Invalid credentials")

        if len(request.password) < 6:
            raise HTTPException(status_code=401, detail="Invalid credentials")

        # For demo purposes, accept any valid email/password combination
        user_id = str(uuid.uuid4())

        # Create access token
        access_token = create_access_token(user_id, request.email)

        return AuthResponse(
            access_token=access_token,
            user={
                "id": user_id,
                "email": request.email,
                "email_confirmed_at": datetime.utcnow().isoformat()
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Sign in error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/signout")
async def sign_out():
    """Sign out user (client-side token removal)"""
    return {"message": "Signed out successfully"}

@router.get("/user")
async def get_user():
    """Get current user info - simplified for now"""
    return {"message": "User endpoint available"}
