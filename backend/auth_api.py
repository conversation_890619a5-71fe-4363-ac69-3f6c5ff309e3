"""
Simple authentication API to replace Supabase auth for local development
"""
import hashlib
import jwt
import uuid
import os
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
from fastapi import APIRouter, HTTPException, Depends, Header
from pydantic import BaseModel
from utils.logger import logger

router = APIRouter(prefix="/auth", tags=["auth"])

# JWT Secret - in production this should be from environment
JWT_SECRET = "your-secret-key-for-local-development"
JWT_ALGORITHM = "HS256"

# File-based user store for demo (in production, use database)
USERS_FILE = "users_db.json"

def load_users():
    """Load users from file"""
    if os.path.exists(USERS_FILE):
        try:
            with open(USERS_FILE, 'r') as f:
                return json.load(f)
        except:
            return {}
    return {}

def save_users(users_db):
    """Save users to file"""
    try:
        with open(USERS_FILE, 'w') as f:
            json.dump(users_db, f, indent=2)
    except Exception as e:
        logger.error(f"Failed to save users: {e}")

# Load existing users
users_db = load_users()

def hash_password(password: str) -> str:
    """Hash password using SHA256"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password: str, hashed_password: str) -> bool:
    """Verify password against hash"""
    return hash_password(password) == hashed_password

class SignUpRequest(BaseModel):
    email: str
    password: str

class SignInRequest(BaseModel):
    email: str
    password: str

class AuthResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    user: dict

def hash_password(password: str) -> str:
    """Hash password using SHA256"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password: str, hashed: str) -> bool:
    """Verify password against hash"""
    return hash_password(password) == hashed

def create_access_token(user_id: str, email: str) -> str:
    """Create JWT access token"""
    expire = datetime.utcnow() + timedelta(hours=24)
    payload = {
        "sub": user_id,
        "email": email,
        "exp": expire,
        "iat": datetime.utcnow(),
        "iss": "suna-local"
    }
    return jwt.encode(payload, JWT_SECRET, algorithm=JWT_ALGORITHM)

@router.post("/signup", response_model=AuthResponse)
async def sign_up(request: SignUpRequest):
    """Register a new user - with local storage"""
    try:
        # Simple email validation
        if "@" not in request.email or "." not in request.email:
            raise HTTPException(status_code=400, detail="Invalid email format")

        if len(request.password) < 6:
            raise HTTPException(status_code=400, detail="Password must be at least 6 characters")

        # Check if user already exists
        if request.email.lower() in users_db:
            raise HTTPException(status_code=400, detail="User already exists")

        # Create new user
        user_id = str(uuid.uuid4())
        hashed_password = hash_password(request.password)

        # Store user in file (for demo)
        users_db[request.email.lower()] = {
            "id": user_id,
            "email": request.email,
            "password_hash": hashed_password,
            "created_at": datetime.utcnow().isoformat(),
            "email_confirmed_at": datetime.utcnow().isoformat()
        }
        save_users(users_db)

        # Create access token
        access_token = create_access_token(user_id, request.email)

        logger.info(f"User registered successfully: {request.email}")

        return AuthResponse(
            access_token=access_token,
            user={
                "id": user_id,
                "email": request.email,
                "email_confirmed_at": datetime.utcnow().isoformat()
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Sign up error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/signin", response_model=AuthResponse)
async def sign_in(request: SignInRequest):
    """Sign in existing user - with real authentication"""
    try:
        # Simple email validation
        if "@" not in request.email or "." not in request.email:
            raise HTTPException(status_code=401, detail="Invalid credentials")

        if len(request.password) < 6:
            raise HTTPException(status_code=401, detail="Invalid credentials")

        # Reload users from file to ensure we have latest data
        current_users = load_users()

        # Check if user exists
        user_data = current_users.get(request.email.lower())
        if not user_data:
            raise HTTPException(status_code=401, detail="Invalid credentials")

        # Verify password
        if not verify_password(request.password, user_data["password_hash"]):
            raise HTTPException(status_code=401, detail="Invalid credentials")

        # Create access token
        access_token = create_access_token(user_data["id"], request.email)

        logger.info(f"User signed in successfully: {request.email}")

        return AuthResponse(
            access_token=access_token,
            user={
                "id": user_data["id"],
                "email": user_data["email"],
                "email_confirmed_at": user_data["email_confirmed_at"]
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Sign in error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/signout")
async def sign_out():
    """Sign out user (client-side token removal)"""
    return {"message": "Signed out successfully"}

def verify_token(authorization: str = Header(None)):
    """Verify JWT token from Authorization header"""
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header missing")

    try:
        # Extract token from "Bearer <token>"
        if not authorization.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Invalid authorization format")

        token = authorization.split(" ")[1]
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token expired")
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token")

@router.get("/user")
async def get_user(token_data: dict = Depends(verify_token)):
    """Get current user info"""
    try:
        email = token_data.get("email")
        user_id = token_data.get("sub")

        if not email or not user_id:
            raise HTTPException(status_code=401, detail="Invalid token data")

        # Reload users from file to ensure we have latest data
        current_users = load_users()

        # Get user from store
        user_data = current_users.get(email.lower())
        if not user_data:
            raise HTTPException(status_code=404, detail="User not found")

        return {
            "id": user_data["id"],
            "email": user_data["email"],
            "email_confirmed_at": user_data["email_confirmed_at"],
            "created_at": user_data["created_at"]
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get user error: {e}")
        raise HTTPException(status_code=500, detail=str(e))
